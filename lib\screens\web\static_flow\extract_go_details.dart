import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:nsl/providers/manual_creation_provider.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/providers/object_creation_provider.dart';
import 'package:nsl/providers/roles_provider.dart';
import 'package:nsl/providers/go_details_provider.dart';
import 'package:nsl/providers/my_library_provider.dart';
import 'package:nsl/models/role_model.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/screens/web/static_flow/extract_lo_details.dart';
import 'package:nsl/screens/web/static_flow/customer_onboarding/widgets/custom_dropdown_widget.dart';
import 'package:provider/provider.dart';

/// Helper class for column width constraints
class ColumnConstraints {
  final double minWidth;
  final double maxWidth;

  const ColumnConstraints({
    required this.minWidth,
    required this.maxWidth,
  });
}

class ExtractGoDetailsMiddleStatic extends StatefulWidget {
  final String? sessionId; // New session-based API support
  final String? userIntent;

  const ExtractGoDetailsMiddleStatic({
    super.key,
    this.sessionId,
    this.userIntent,
  });

  @override
  State<ExtractGoDetailsMiddleStatic> createState() =>
      _ExtractGoDetailsMiddleStaticState();
}

class _ExtractGoDetailsMiddleStaticState
    extends State<ExtractGoDetailsMiddleStatic> {
  @override
  void initState() {
    super.initState();
    // Initialize library provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final libraryProvider =
          Provider.of<MyLibraryProvider>(context, listen: false);
      libraryProvider.fetchLibraryData();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer4<WebHomeProviderStatic, ObjectCreationProvider,
        RolesProvider, GoDetailsProvider>(
      builder: (context, provider, objectCreationProvider, rolesProvider,
          goDetailsProvider, child) {
        return Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.white,
          child: Column(
            children: [
              // Header with toggle
              // _buildHeader(context, provider),

              // Content area
              Expanded(
                child: _buildContent(
                    context, provider, rolesProvider, goDetailsProvider),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, WebHomeProviderStatic provider) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: const BoxDecoration(
        // color: Colors.black,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE5E7EB), width: .5),
        ),
        // boxShadow: [
        //   BoxShadow(
        //     color: Color(0x1A000000), // Black with 10% opacity
        //     blurRadius: 8,
        //     offset: Offset(0, 2),
        //     spreadRadius: 0,
        //   ),
        // ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Dynamic header label based on toggle state
          Text(
            // provider.isAIMode ? 'Objects' : 'Extracted Details',
            'Extracted Details',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleSmall(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
              fontWeight: FontWeight.bold,
              height: 1,
            ),
          ),

          // Right side with toggle and manually process text
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // AI/Manual Toggle
              _buildAIManualToggle(context, provider),

              const SizedBox(width: 16),

              // // Manually Process text
              // Text(
              //   'Manually Process',
              //   style: FontManager.getCustomStyle(
              //     fontSize: ResponsiveFontSizes.titleSmall(context),
              //     color: Colors.white,
              //     fontWeight: FontWeight.w400,
              //     fontFamily: FontManager.fontFamilyTiemposText,
              //     height: 1,
              //   ),
              // ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAIManualToggle(
      BuildContext context, WebHomeProviderStatic provider) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Toggle switch
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              // Close entity details panel if it's open before switching modes
              final manualProvider =
                  Provider.of<ManualCreationProvider>(context, listen: false);
              if (manualProvider.selectedEntity != null) {
                manualProvider.setSelectedEntity(null);
              }

              provider.toggleAIMode();
              manualProvider.handleEntityValidationForBook();
            },
            child: Container(
              width: 34,
              height: 18,
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(
                  color: Colors.black,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(10),
              ),
              child: AnimatedAlign(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeInOut,
                alignment: provider.isAIMode
                    ? Alignment.centerLeft
                    : Alignment.centerRight,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Color(0xFF0058FF),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
          ),
        ),

        const SizedBox(width: 4),

        // ai label on the right
        Text(
          'Form',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            color: Colors.black,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            height: 1,
          ),
        ),
        const SizedBox(width: 12),
        // manua; label on the left
        Text(
          'Manual Process',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            color: Colors.black,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            height: 1,
          ),
        ),
      ],
    );
  }

  Widget _buildContent(BuildContext context, WebHomeProviderStatic provider,
      RolesProvider rolesProvider, GoDetailsProvider goDetailsProvider) {
    return Container(
      padding: const EdgeInsets.all(12),
      child: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(
          scrollbars: false,
          physics: const AlwaysScrollableScrollPhysics(),
        ),
        child: LayoutBuilder(
          builder: (context, constraints) {
            final screenHeight = MediaQuery.of(context).size.height;
            final availableHeight =
                screenHeight - 180; // Account for header and padding

            return SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  // FIXED: Use dynamic height based on content but ensure minimum
                  minHeight: availableHeight,
                  // Allow unlimited height for overflow content
                  maxHeight: double.infinity,
                ),
                child: IntrinsicHeight(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Main content - this will expand as needed
                      Flexible(
                        fit: FlexFit.loose,
                        child: _buildContentWithLineNumbers(
                            context, rolesProvider, goDetailsProvider),
                      ),
                      // Bottom spacing for dropdowns
                      const SizedBox(height: 300),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildEntityFirstRow(BuildContext context,
      MyLibraryProvider libraryProvider, GoDetailsProvider goDetailsProvider) {
    return Row(
      children: [
        // Solution text (clickable to edit) - ONLY for solution field
        Expanded(
          flex: 8, // Same flex as the solution text field in edit mode
          child: MouseRegion(
            cursor: SystemMouseCursors.click,
            child: InkWell(
              onTap: () {
                goDetailsProvider.updateEditMode(true);
              },
              child: Text(
                'Solution: ${goDetailsProvider.solutionController.text.isEmpty ? 'Not specified' : goDetailsProvider.solutionController.text}',
                style: TextStyle(
                    fontSize: ResponsiveFontSizes.labelMedium(context),
                    fontWeight: FontWeight.w700,
                    color: Colors.black,
                    fontFamily: FontManager.fontFamilyTiemposText),
              ),
            ),
          ),
        ),
        const SizedBox(width: AppSpacing.xs), // Same spacing as edit mode

        // Agent Type - Keep it exactly same as in edit mode
        Text(
          'Agent Type:',
          style: TextStyle(
              fontSize: ResponsiveFontSizes.labelMedium(context),
              fontWeight: FontWeight.w700,
              color: Colors.black,
              fontFamily: FontManager.fontFamilyTiemposText),
        ),
        SizedBox(width: AppSpacing.xxs), // Same spacing as edit mode
        Expanded(
          flex: 1, // Same flex as in edit mode
          child: Consumer<MyLibraryProvider>(
            builder: (context, libraryProvider, child) {
              return _buildAgentTypeDropdown(
                  context, libraryProvider, goDetailsProvider);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildContentWithLineNumbers(BuildContext context,
      RolesProvider rolesProvider, GoDetailsProvider goDetailsProvider) {
    int lineNumber = 1;
    final List<Widget> allWidgets = [];
    if (goDetailsProvider.showLocalObjectiveDetails) {
      return ExtractLoDetailsMiddleStatic();
    }

    // Edit mode logic - similar to _InnerEntityScreenState
    if (goDetailsProvider.isEditMode) {
      // Line 1: Solution row (editable)
      allWidgets
          .add(_buildLineWithNumber(lineNumber++, Consumer<MyLibraryProvider>(
        builder: (context, libraryProvider, child) {
          return _buildFirstRow(context, libraryProvider, goDetailsProvider);
        },
      )));
      allWidgets.add(const SizedBox(height: 16));

      // Line 2: Description row (editable)
      allWidgets.add(_buildLineWithNumber(
          lineNumber++, _buildSecondRow(context, goDetailsProvider)));
    } else {
      // Show read-only version
      allWidgets
          .add(_buildLineWithNumber(lineNumber++, Consumer<MyLibraryProvider>(
        builder: (context, libraryProvider, child) {
          return _buildEntityFirstRow(
              context, libraryProvider, goDetailsProvider);
        },
      )));
    }

    // Show local objectives section after validation
    if (goDetailsProvider.currentStep == GoDetailsStep.afterValidation ||
        goDetailsProvider.currentStep == GoDetailsStep.afterLocalObjectives) {
      allWidgets.add(const SizedBox(height: 24));

      // Line 3: LOCAL OBJECTIVES header
      allWidgets.add(
          _buildLineWithNumber(lineNumber++, _buildLocalObjectivesHeader()));
      allWidgets.add(const SizedBox(height: 8));

      // Local objectives content with line numbers
      if (goDetailsProvider.currentStep == GoDetailsStep.afterLocalObjectives) {
        // Show LO list with line numbers
        for (final entry in goDetailsProvider
            .currentGoModel!.localObjectivesList!
            .asMap()
            .entries) {
          final index = entry.key;
          final objective = entry.value;

          // Add spacing before each LO (except the first one)
          if (index > 0) {
            allWidgets.add(const SizedBox(height: 12));
          }

          // Add LO item
          allWidgets.add(_buildLineWithNumber(
            lineNumber++,
            _buildLocalObjectiveItem(
                index, objective.name ?? '', goDetailsProvider),
          ));

          // Add LO insertion text field if open for this LO
          if (goDetailsProvider.isLoInsertionOpen(index)) {
            allWidgets.add(_buildLineWithNumber(
              lineNumber++,
              _buildLoInsertionField(index, goDetailsProvider),
            ));
          }

          // Add pathway creation fields if open for this LO
          if (goDetailsProvider.isPathwayCreationOpen(index)) {
            final selectedType =
                goDetailsProvider.getPathwaySelectedType(index);

            if (selectedType == 'Alternative' || selectedType == 'Parallel') {
              // Add Apply Condition section with individual line numbers
              List<Widget> conditionWidgets =
                  _buildApplyConditionFieldsWithLineNumbers(
                      index, goDetailsProvider, rolesProvider, lineNumber);

              allWidgets.add(
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: conditionWidgets,
                ),
              );
              lineNumber +=
                  4; // 4 rows: header + first condition + second condition + +LO
            } else if (selectedType == 'Sequential' ||
                selectedType == 'Recursive' ||
                selectedType == 'Terminal') {
              // Add blue container with individual line numbers for Sequential, Recursive, Terminal
              allWidgets.addAll(
                  _buildSequentialRecursiveTerminalFieldsWithLineNumbers(
                      index, goDetailsProvider, rolesProvider, lineNumber));
              lineNumber += 1; // 1 row for these types
            } else {
              // For initial state (no type selected) or any other types - wrap in blue container
              allWidgets.addAll(_buildInitialPathwayFieldsWithLineNumbers(
                  index, goDetailsProvider, rolesProvider, lineNumber));
              lineNumber += 1; // 1 row for initial fields
            }
          }
        }
      } else {
        // Line 4: Input field
        allWidgets.add(_buildLineWithNumber(lineNumber++,
            _buildLocalObjectiveInput(context, goDetailsProvider)));
      }
    }

    // Add responsive spacing at the end to extend the vertical line
    final screenHeight = MediaQuery.of(context).size.height;
    final dynamicSpacing = screenHeight * 0.8; // 50% of screen height
    allWidgets.add(SizedBox(height: dynamicSpacing));

    return Stack(
      children: [
        // Content
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: allWidgets,
        ),
        // Continuous vertical line - positioned to extend through all content
        Positioned(
          left: 28, // Position after line number (20px width + 8px margin)
          top: 0,
          bottom: 0,
          child: Container(
            width: 1,
            color: Colors.grey.shade300,
          ),
        ),
      ],
    );
  } // Widget _buildContentWithLineNumbers(BuildContext context,
  //     RolesProvider rolesProvider, GoDetailsProvider goDetailsProvider) {
  //   int lineNumber = 1;
  //   final List<Widget> allWidgets = [];
  //   if (goDetailsProvider.showLocalObjectiveDetails) {
  //     return ExtractLoDetailsMiddleStatic();
  //   }

  //   // Line 1: Solution row
  //   allWidgets
  //       .add(_buildLineWithNumber(lineNumber++, Consumer<MyLibraryProvider>(
  //     builder: (context, libraryProvider, child) {
  //       return _buildFirstRow(context, libraryProvider, goDetailsProvider);
  //     },
  //   )));
  //   allWidgets.add(const SizedBox(height: 16));

  //   // Line 2: Description row
  //   allWidgets.add(_buildLineWithNumber(
  //       lineNumber++, _buildSecondRow(context, goDetailsProvider)));

  //   // Show local objectives section after validation
  //   if (goDetailsProvider.currentStep == GoDetailsStep.afterValidation ||
  //       goDetailsProvider.currentStep == GoDetailsStep.afterLocalObjectives) {
  //     allWidgets.add(const SizedBox(height: 24));

  //     // Line 3: LOCAL OBJECTIVES header
  //     allWidgets.add(
  //         _buildLineWithNumber(lineNumber++, _buildLocalObjectivesHeader()));
  //     allWidgets.add(const SizedBox(height: 8));

  //     // Local objectives content with line numbers
  //     if (goDetailsProvider.currentStep == GoDetailsStep.afterLocalObjectives) {
  //       // Show LO list with line numbers
  //       for (final entry in goDetailsProvider
  //           .currentGoModel!.localObjectivesList!
  //           .asMap()
  //           .entries) {
  //         final index = entry.key;
  //         final objective = entry.value;

  //         // Add spacing before each LO (except the first one)
  //         if (index > 0) {
  //           allWidgets.add(const SizedBox(height: 12));
  //         }

  //         // Add LO item
  //         allWidgets.add(_buildLineWithNumber(
  //           lineNumber++,
  //           _buildLocalObjectiveItem(
  //               index, objective.name ?? '', goDetailsProvider),
  //         ));

  //         // Add LO insertion text field if open for this LO
  //         if (goDetailsProvider.isLoInsertionOpen(index)) {
  //           allWidgets.add(_buildLineWithNumber(
  //             lineNumber++,
  //             _buildLoInsertionField(index, goDetailsProvider),
  //           ));
  //         }

  //         // Add pathway creation fields if open for this LO
  //         if (goDetailsProvider.isPathwayCreationOpen(index)) {
  //           final selectedType =
  //               goDetailsProvider.getPathwaySelectedType(index);

  //           if (selectedType == 'Alternative' || selectedType == 'Parallel') {
  //             // Add Apply Condition section with individual line numbers
  //             List<Widget> conditionWidgets =
  //                 _buildApplyConditionFieldsWithLineNumbers(
  //                     index, goDetailsProvider, rolesProvider, lineNumber);

  //             allWidgets.add(
  //               Column(
  //                 crossAxisAlignment: CrossAxisAlignment.start,
  //                 children: conditionWidgets,
  //               ),
  //             );
  //             lineNumber +=
  //                 4; // 4 rows: header + first condition + second condition + +LO
  //           } else if (selectedType == 'Sequential' ||
  //               selectedType == 'Recursive' ||
  //               selectedType == 'Terminal') {
  //             // Add blue container with individual line numbers for Sequential, Recursive, Terminal
  //             allWidgets.addAll(
  //                 _buildSequentialRecursiveTerminalFieldsWithLineNumbers(
  //                     index, goDetailsProvider, rolesProvider, lineNumber));
  //             lineNumber += 1; // 1 row for these types
  //           } else {
  //             // For initial state (no type selected) or any other types - wrap in blue container
  //             allWidgets.addAll(_buildInitialPathwayFieldsWithLineNumbers(
  //                 index, goDetailsProvider, rolesProvider, lineNumber));
  //             lineNumber += 1; // 1 row for initial fields
  //           }
  //         }
  //       }
  //     } else {
  //       // Line 4: Input field
  //       allWidgets.add(_buildLineWithNumber(lineNumber++,
  //           _buildLocalObjectiveInput(context, goDetailsProvider)));
  //     }
  //   }

  //   // Add responsive spacing at the end to extend the vertical line
  //   final screenHeight = MediaQuery.of(context).size.height;
  //   final dynamicSpacing = screenHeight * 0.8; // 50% of screen height
  //   allWidgets.add(SizedBox(height: dynamicSpacing));

  //   return Stack(
  //     children: [
  //       // Content
  //       Column(
  //         crossAxisAlignment: CrossAxisAlignment.start,
  //         children: allWidgets,
  //       ),
  //       // Continuous vertical line - positioned to extend through all content
  //       Positioned(
  //         left: 28, // Position after line number (20px width + 8px margin)
  //         top: 0,
  //         bottom: 0,
  //         child: Container(
  //           width: 1,
  //           color: Colors.grey.shade300,
  //         ),
  //       ),
  //     ],
  //   );
  // }

  List<Widget> _buildApplyConditionFieldsWithLineNumbers(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      int startLineNumber) {
    final widgets = <Widget>[];
    int currentLineNumber = startLineNumber;
    final dropdownWidth = _getDropdownWidth(context);
    final dropdownAttributeWidth = _getAttributeDropdownWidth(context);

    // Row 1: Apply Condition header with line number outside container
    widgets.add(_buildLineWithNumber(
      currentLineNumber++,
      Container(
        width: double.infinity, // This ensures full width
        decoration: const BoxDecoration(
          color: Color(0xFFF7F7F7),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(4),
            topRight: Radius.circular(4),
          ),
        ),
        padding: const EdgeInsets.all(8),
        child: const Center(
          child: Text(
            'Apply Condition',
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: Colors.black,
              fontFamily: FontManager.fontFamilyTiemposText,
            ),
          ),
        ),
      ),
    ));

    // Row 2: Role and Type dropdowns with first pathway entry
    widgets.add(_buildLineWithNumber(
      currentLineNumber++,
      Container(
        width: double.infinity, // Full width
        decoration: const BoxDecoration(
          color: Color(0xFFF7F7F7),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        child: Row(
          children: [
            // Use Expanded to make dropdowns take available space
            Expanded(
              flex: 2,
              child: Consumer<MyLibraryProvider>(
                builder: (context, libraryProvider, child) {
                  return _buildPathwayRoleField(
                      loIndex, goDetailsProvider, libraryProvider);
                },
              ),
            ),
            const SizedBox(width: 8),

            Expanded(
              flex: 2,
              child: _buildPathwayTypeField(loIndex, goDetailsProvider),
            ),
            const SizedBox(width: 8),

            Expanded(
              flex: 2,
              child: _buildDynamicLODropdown(loIndex, 0, goDetailsProvider),
            ),
            const SizedBox(width: 16),

            Expanded(
              flex: 3,
              child: _buildDynamicEntityAttributeDropdown(
                loIndex: loIndex,
                entryIndex: 0,
                isAfterCondition: false,
                goDetailsProvider: goDetailsProvider,
              ),
            ),
            const SizedBox(width: 8),

            Expanded(
              flex: 2,
              child: _buildDynamicConditionDropdown(
                loIndex: loIndex,
                entryIndex: 0,
                goDetailsProvider: goDetailsProvider,
              ),
            ),
            const SizedBox(width: 8),

            Expanded(
              flex: 3,
              child: _buildDynamicEntityAttributeDropdown(
                loIndex: loIndex,
                entryIndex: 0,
                isAfterCondition: true,
                goDetailsProvider: goDetailsProvider,
              ),
            ),
          ],
        ),
      ),
    ));

    // Dynamic pathway entry rows (starting from index 1)
    final pathwayEntries = goDetailsProvider.getPathwayEntries(loIndex);
    final entryCount = pathwayEntries.length;

    if (entryCount == 0) {
      goDetailsProvider.addPathwayEntry(loIndex);
      goDetailsProvider.addPathwayEntry(loIndex);
    } else if (entryCount == 1) {
      goDetailsProvider.addPathwayEntry(loIndex);
    }

    final updatedEntryCount =
        goDetailsProvider.getPathwayEntries(loIndex).length;
    for (int entryIndex = 1;
        entryIndex < math.max(2, updatedEntryCount);
        entryIndex++) {
      widgets.add(_buildLineWithNumber(
        currentLineNumber++,
        Container(
          width: double.infinity, // Full width
          decoration: const BoxDecoration(
            color: Color(0xFFF7F7F7),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          child: Row(
            children: [
              // Empty space to align with dropdowns above
              Expanded(flex: 2, child: Container()),
              const SizedBox(width: 8),
              Expanded(flex: 2, child: Container()),
              const SizedBox(width: 8),

              Expanded(
                flex: 2,
                child: _buildDynamicLODropdown(
                    loIndex, entryIndex, goDetailsProvider),
              ),
              const SizedBox(width: 16),

              Expanded(
                flex: 3,
                child: _buildDynamicEntityAttributeDropdown(
                  loIndex: loIndex,
                  entryIndex: entryIndex,
                  isAfterCondition: false,
                  goDetailsProvider: goDetailsProvider,
                ),
              ),
              const SizedBox(width: 8),

              Expanded(
                flex: 2,
                child: _buildDynamicConditionDropdown(
                  loIndex: loIndex,
                  entryIndex: entryIndex,
                  goDetailsProvider: goDetailsProvider,
                ),
              ),
              const SizedBox(width: 8),

              Expanded(
                flex: 3,
                child: _buildDynamicEntityAttributeDropdown(
                  loIndex: loIndex,
                  entryIndex: entryIndex,
                  isAfterCondition: true,
                  goDetailsProvider: goDetailsProvider,
                ),
              ),
            ],
          ),
        ),
      ));
    }

    // Add +LO button in a separate row
    widgets.add(_buildLineWithNumber(
      currentLineNumber++,
      Container(
        width: double.infinity, // Full width
        decoration: const BoxDecoration(
          color: Color(0xFFF7F7F7),
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(4),
            bottomRight: Radius.circular(4),
          ),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        child: Row(
          children: [
            const Spacer(), // Push the +LO button to the right
            InkWell(
              onTap: () {
                goDetailsProvider.addPathwayEntry(loIndex);
              },
              child: const Text(
                '+ LO',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: Colors.blue,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
            ),
          ],
        ),
      ),
    ));

    return widgets;
  } // Build initial pathway fields (Role and Type dropdowns) with blue container and line numbers

  List<Widget> _buildInitialPathwayFieldsWithLineNumbers(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      int startLineNumber) {
    final widgets = <Widget>[];
    int currentLineNumber = startLineNumber;
    final dropdownWidth = _getDropdownWidth(context);

    // Single row with blue background container and line number outside container
    widgets.add(_buildLineWithNumber(
      currentLineNumber++,
      Container(
        width: double.infinity,
        decoration: const BoxDecoration(
          color:
              Color(0xFFF7F7F7), // Blue background (same as other containers)
          borderRadius: BorderRadius.all(Radius.circular(4)),
        ),
        padding: const EdgeInsets.all(12),
        child: _buildInitialPathwayContent(
            loIndex, goDetailsProvider, rolesProvider, dropdownWidth),
      ),
    ));

    return widgets;
  }

  // Build content for initial pathway creation (Role and Type dropdowns only)
  Widget _buildInitialPathwayContent(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      double dropdownWidth) {
    return Row(
      children: [
        // Select Role dropdown
        SizedBox(
          width: dropdownWidth,
          child: Consumer<MyLibraryProvider>(
            builder: (context, libraryProvider, child) {
              return _buildPathwayRoleField(
                  loIndex, goDetailsProvider, libraryProvider);
            },
          ),
        ),
        const SizedBox(width: 8),

        // Select Type dropdown
        SizedBox(
          width: dropdownWidth,
          child: _buildPathwayTypeField(loIndex, goDetailsProvider),
        ),
        // No third field for initial state
      ],
    );
  }

  // Build Sequential, Recursive, Terminal fields with individual line numbers and blue container
  List<Widget> _buildSequentialRecursiveTerminalFieldsWithLineNumbers(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      int startLineNumber) {
    final widgets = <Widget>[];
    int currentLineNumber = startLineNumber;
    final dropdownWidth = _getDropdownWidth(context);

    // Single row with blue background container and line number outside container
    widgets.add(_buildLineWithNumber(
      currentLineNumber++,
      Container(
        width: double.infinity,
        decoration: const BoxDecoration(
          color: Color(0xFFF7F7F7),
          borderRadius: BorderRadius.all(Radius.circular(4)),
        ),
        padding: const EdgeInsets.all(12),
        child: _buildSequentialRecursiveTerminalContent(
            loIndex, goDetailsProvider, rolesProvider, dropdownWidth),
      ),
    ));

    return widgets;
  }

  // Build content for Sequential, Recursive, Terminal types
  Widget _buildSequentialRecursiveTerminalContent(
      int loIndex,
      GoDetailsProvider goDetailsProvider,
      RolesProvider rolesProvider,
      double dropdownWidth) {
    final selectedType = goDetailsProvider.getPathwaySelectedType(loIndex);

    return Row(
      children: [
        // Select Role dropdown
        SizedBox(
          width: dropdownWidth,
          child: Consumer<MyLibraryProvider>(
            builder: (context, libraryProvider, child) {
              return _buildPathwayRoleField(
                  loIndex, goDetailsProvider, libraryProvider);
            },
          ),
        ),
        const SizedBox(width: 8),

        // Select Type dropdown
        SizedBox(
          width: dropdownWidth,
          child: _buildPathwayTypeField(loIndex, goDetailsProvider),
        ),

        // Third field based on selected type
        if (selectedType == 'Sequential') ...[
          const SizedBox(width: 8),
          SizedBox(
            width: dropdownWidth,
            child: _buildPathwayLOField(loIndex, goDetailsProvider),
          ),
        ] else if (selectedType == 'Recursive') ...[
          const SizedBox(width: 8),
          SizedBox(
            width: dropdownWidth,
            child: _buildRecursiveInputField(loIndex, goDetailsProvider),
          ),
        ],
        // Terminal type has no third field
        if (selectedType == 'Terminal') ...[
          const SizedBox(width: 8),
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white,
                border: Border.all(color: Color(0xFFFF9390), width: 0.5)),
            child: Icon(Icons.close, color: Color(0xFFFF211B), size: 14),
          ),
        ],
      ],
    );
  }

  Widget _buildFirstRow(BuildContext context, MyLibraryProvider libraryProvider,
      GoDetailsProvider goDetailsProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            // Solution label and text field with validation
            Text(
              'Solution:',
              style: TextStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w700,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText),
            ),
            const SizedBox(width: 28),
            Expanded(
              flex: 8, // This flex should match the read-only version
              child: Container(
                height: 35,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(2),
                ),
                child: TextField(
                  controller: goDetailsProvider.solutionController,
                  onChanged: (value) {
                    // Clear validation error when user starts typing
                    if (goDetailsProvider.solutionValidationError != null) {
                      goDetailsProvider.clearValidationErrors();
                    }
                  },
                  decoration: const InputDecoration(
                    hintText: 'Type here..',
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    fillColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    contentPadding: EdgeInsets.symmetric(
                        horizontal: 4, vertical: 12), // Remove default padding
                    isDense: true,
                  ),
                  style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText),
                ),
              ),
            ),
            const SizedBox(width: AppSpacing.xs), // Same spacing as read-only
            Text(
              'Agent Type:',
              style: TextStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w700,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText),
            ),
            SizedBox(width: AppSpacing.xs), // Same spacing as read-only
            Expanded(
              flex: 1, // Same flex as read-only version
              child: Consumer<MyLibraryProvider>(
                builder: (context, libraryProvider, child) {
                  return _buildAgentTypeDropdown(
                      context, libraryProvider, goDetailsProvider);
                },
              ),
            ),
          ],
        ),
        // Validation error message - placed outside the Row to avoid layout issues
        if (goDetailsProvider.solutionValidationError != null)
          Padding(
            padding: EdgeInsets.only(
                left: MediaQuery.of(context).size.width * 0.048,
                top: 2), // Align with text field
            child: Text(
              goDetailsProvider.solutionValidationError!,
              style: const TextStyle(
                fontSize: 8,
                color: Colors.red,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          ),
      ],
    );
  }

  // Widget _buildFirstRow(BuildContext context, MyLibraryProvider libraryProvider,
  //     GoDetailsProvider goDetailsProvider) {
  //   return Row(
  //     children: [
  //       // Solution label and text field with validation
  //       Expanded(
  //         // flex: 3,
  //         child: Column(
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           children: [
  //             Row(
  //               children: [
  //                 Text(
  //                   'Solution:',
  //                   style: TextStyle(
  //                       fontSize: ResponsiveFontSizes.labelMedium(context),
  //                       fontWeight: FontWeight.w700,
  //                       color: Colors.black,
  //                       fontFamily: FontManager.fontFamilyTiemposText),
  //                 ),
  //                 const SizedBox(width: 28),
  //                 Expanded(
  //                   flex: 8,
  //                   child: Container(
  //                     height: 35,
  //                     decoration: BoxDecoration(
  //                       border: Border.all(color: Colors.grey.shade300),
  //                       borderRadius: BorderRadius.circular(2),
  //                     ),
  //                     child: TextField(
  //                       controller: goDetailsProvider.solutionController,
  //                       onChanged: (value) {
  //                         // Clear validation error when user starts typing
  //                         if (goDetailsProvider.solutionValidationError !=
  //                             null) {
  //                           goDetailsProvider.clearValidationErrors();
  //                         }
  //                       },
  //                       decoration: const InputDecoration(
  //                         hintText: 'Type here..',
  //                         border: InputBorder.none,
  //                         enabledBorder: InputBorder.none,
  //                         focusedBorder: InputBorder.none,
  //                         fillColor: Colors.transparent,
  //                         hoverColor: Colors.transparent,
  //                         contentPadding: EdgeInsets.symmetric(
  //                             horizontal: 4,
  //                             vertical: 12), // Remove default padding
  //                         isDense: true,
  //                       ),
  //                       style: TextStyle(
  //                           fontSize: 10,
  //                           fontWeight: FontWeight.w500,
  //                           color: Colors.black,
  //                           fontFamily: FontManager.fontFamilyTiemposText),
  //                     ),
  //                   ),
  //                 ),
  //                 const SizedBox(width: AppSpacing.xs),
  //                 Text(
  //                   'Agent Type:',
  //                   style: TextStyle(
  //                      fontSize: ResponsiveFontSizes.labelMedium(context),
  //                       fontWeight: FontWeight.w700,
  //                       color: Colors.black,
  //                       fontFamily: FontManager.fontFamilyTiemposText),
  //                 ),

  //                 // Tick icon button
  //                 // InkWell(
  //                 //   onTap: goDetailsProvider.isValidating
  //                 //       ? null
  //                 //       : () async {
  //                 //           // First validate the solution field
  //                 //           if (!goDetailsProvider.validateSolutionField()) {
  //                 //             return; // Stop if validation fails
  //                 //           }

  //                 //           // Create GoModel with collected data before validation
  //                 //           goDetailsProvider.currentGoModel =
  //                 //               goDetailsProvider.createGoModelFromControllers(
  //                 //                   goDetailsProvider);

  //                 //           // Pass the GoModel to validation
  //                 //           await goDetailsProvider.validateSolutionWithGoModel(
  //                 //               goDetailsProvider.currentGoModel);
  //                 //         },
  //                 //   child: Container(
  //                 //     width: 24,
  //                 //     height: 24,
  //                 //     decoration: BoxDecoration(
  //                 //       color: const Color(0xFF007AFF),
  //                 //       shape: BoxShape.circle,
  //                 //     ),
  //                 //     child: const Icon(
  //                 //       Icons.check,
  //                 //       color: Colors.white,
  //                 //       size: 14,
  //                 //     ),
  //                 //   ),
  //                 // ),

  //                 SizedBox(width: AppSpacing.xs),
  //                 Expanded(
  //                   flex: 1,
  //                   child: Consumer<MyLibraryProvider>(
  //                     builder: (context, libraryProvider, child) {
  //                       return _buildAgentTypeDropdown(
  //                           context, libraryProvider, goDetailsProvider);
  //                     },
  //                   ),
  //                 ),
  //               ],
  //             ), // Validation error message
  //             if (goDetailsProvider.solutionValidationError != null)
  //               Padding(
  //                 padding: EdgeInsets.only(
  //                     left: MediaQuery.of(context).size.width * 0.048,
  //                     top: 2), // Align with text field
  //                 child: Text(
  //                   goDetailsProvider.solutionValidationError!,
  //                   style: const TextStyle(
  //                     fontSize: 8,
  //                     color: Colors.red,
  //                     fontFamily: FontManager.fontFamilyTiemposText,
  //                   ),
  //                 ),
  //               ),
  //           ],
  //         ),
  //       ),
  //     ],
  //   );
  // }

  Widget _buildAgentTypeDropdown(BuildContext context,
      MyLibraryProvider libraryProvider, GoDetailsProvider goDetailsProvider) {
    // Fetch library data if not already loaded
    // if (!libraryProvider.isLoading && libraryProvider.roles.isEmpty) {
    //   WidgetsBinding.instance.addPostFrameCallback((_) {
    //     libraryProvider.fetchLibraryData();
    //   });
    // }

    return Row(
      children: [
        Expanded(
          child: SizedBox(
            height: 35,
            child: CustomDropdownWidget(
              label: 'Role Type',
              list: libraryProvider.isLoading
                  ? ['Loading roles...']
                  : libraryProvider.roles.isEmpty
                      ? ['No roles available']
                      : libraryProvider.getRoleNames(),
              value: goDetailsProvider.selectedRoles
                  .map((role) => role.name ?? '')
                  .toList(),
              isMultiSelect: true,
              onChanged: (selectedValues) {
                if (!libraryProvider.isLoading &&
                    selectedValues is List<String>) {
                  // Filter out loading/error messages
                  final validRoleNames = selectedValues
                      .where((value) =>
                          value != 'Loading roles...' &&
                          value != 'No roles available')
                      .toList();

                  // Convert role names to PostgresRole objects
                  final selectedRoles = <PostgresRole>[];
                  for (final roleName in validRoleNames) {
                    final selectedLibraryRole =
                        libraryProvider.getRoleByName(roleName);
                    if (selectedLibraryRole != null) {
                      final postgresRole = PostgresRole(
                        roleId: selectedLibraryRole.roleId,
                        name: selectedLibraryRole.name,
                        description: selectedLibraryRole.description,
                        tenantId: null,
                        createdAt: selectedLibraryRole.createdAt,
                        updatedAt: selectedLibraryRole.updatedAt,
                      );
                      selectedRoles.add(postgresRole);
                    }
                  }

                  goDetailsProvider.setSelectedRoles(selectedRoles);
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSecondRow(
      BuildContext context, GoDetailsProvider goDetailsProvider) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Text(
              'Description:',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w700,
                color: Colors.black,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
            const SizedBox(width: AppSpacing.xs),
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      height: 35,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(2),
                      ),
                      child: TextField(
                        controller: goDetailsProvider.descriptionController,
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          isDense: true,
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          fillColor: Colors.transparent,
                          hoverColor: Colors.transparent,
                          contentPadding:
                              EdgeInsets.symmetric(horizontal: 4, vertical: 12),
                          hintText: 'Enter description..',
                        ),
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                          fontFamily: FontManager.fontFamilyTiemposText,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Align(
          alignment: Alignment.centerRight,
          child: InkWell(
            onTap: goDetailsProvider.isValidating
                ? null
                : () async {
                    // First validate the solution field
                    if (!goDetailsProvider.validateSolutionField()) {
                      return; // Stop if validation fails
                    }

                    // Create GoModel with collected data before validation
                    goDetailsProvider.currentGoModel = goDetailsProvider
                        .createGoModelFromControllers(goDetailsProvider);

                    // Pass the GoModel to validation
                    await goDetailsProvider.validateSolutionWithGoModel(
                        goDetailsProvider.currentGoModel);

                    // ADDED: Switch to read-only mode after validation
                    goDetailsProvider.updateEditMode(false);
                  },
            borderRadius: BorderRadius.circular(2),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(2),
              ),
              child: const Text(
                'Validate',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w400,
                  color: Colors.black,
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
            ),
          ),
        )
      ],
    );
  }

  // Widget _buildSecondRow(
  //     BuildContext context, GoDetailsProvider goDetailsProvider) {
  //   return Column(
  //     children: [
  //       Row(
  //         crossAxisAlignment: CrossAxisAlignment.center,
  //         children: [
  //           const Text(
  //             'Description:',
  //             style: TextStyle(
  //               fontSize: 12,
  //               fontWeight: FontWeight.w700,
  //               color: Colors.black,
  //               fontFamily: FontManager.fontFamilyTiemposText,
  //             ),
  //           ),
  //           const SizedBox(width: AppSpacing.xs),
  //           Expanded(
  //             child: Row(
  //               children: [
  //                 Expanded(
  //                   child: Container(
  //                     height: 35,
  //                     decoration: BoxDecoration(
  //                       border: Border.all(color: Colors.grey.shade300),
  //                       borderRadius: BorderRadius.circular(2),
  //                     ),
  //                     child: TextField(
  //                       controller: goDetailsProvider.descriptionController,
  //                       decoration: const InputDecoration(
  //                         border: InputBorder.none,
  //                         isDense: true,
  //                         enabledBorder: InputBorder.none,
  //                         focusedBorder: InputBorder.none,
  //                         fillColor: Colors.transparent,
  //                         hoverColor: Colors.transparent,
  //                         contentPadding:
  //                             EdgeInsets.symmetric(horizontal: 4, vertical: 12),
  //                         hintText: 'Enter description..',
  //                       ),
  //                       style: TextStyle(
  //                         fontSize: 10,
  //                         fontWeight: FontWeight.w500,
  //                         color: Colors.black,
  //                         fontFamily: FontManager.fontFamilyTiemposText,
  //                       ),
  //                     ),
  //                   ),
  //                 ),
  //               ],
  //             ),
  //           ),
  //         ],
  //       ),
  //       const SizedBox(height: 8),
  //       Align(
  //         alignment: Alignment.centerRight,
  //         child: InkWell(
  //           onTap: goDetailsProvider.isValidating
  //               ? null
  //               : () async {
  //                   // First validate the solution field
  //                   if (!goDetailsProvider.validateSolutionField()) {
  //                     return; // Stop if validation fails
  //                   }

  //                   // Create GoModel with collected data before validation
  //                   goDetailsProvider.currentGoModel = goDetailsProvider
  //                       .createGoModelFromControllers(goDetailsProvider);

  //                   // Pass the GoModel to validation
  //                   await goDetailsProvider.validateSolutionWithGoModel(
  //                       goDetailsProvider.currentGoModel);
  //                 },
  //           borderRadius: BorderRadius.circular(2),
  //           child: Container(
  //             padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
  //             decoration: BoxDecoration(
  //               border: Border.all(color: Colors.grey.shade300),
  //               borderRadius: BorderRadius.circular(2),
  //             ),
  //             child: const Text(
  //               'Validate',
  //               style: TextStyle(
  //                 fontSize: 10,
  //                 fontWeight: FontWeight.w400,
  //                 color: Colors.black,
  //                 fontFamily: FontManager.fontFamilyTiemposText,
  //               ),
  //             ),
  //           ),
  //         ),
  //       )
  //     ],
  //   );
  // }

  // Helper methods for line numbers
  Widget _buildLineWithNumber(int lineNumber, Widget content) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Line number
        SizedBox(
          // color: Color(0xFFEDF3FF),
          width: 20,
          child: Text(
            '$lineNumber',
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w400,
              color: Colors.black,
              fontFamily: FontManager.fontFamilyInter,
            ),
          ),
        ),
        // Space for the continuous vertical line
        const SizedBox(width: 17), // 8px margin + 1px line + 8px margin
        // Content
        Expanded(child: content),
      ],
    );
  }

  Widget _buildLocalObjectivesHeader() {
    return Text(
      'LOCAL OBJECTIVES',
      style: TextStyle(
        fontSize: ResponsiveFontSizes.labelMedium(context),
        fontWeight: FontWeight.w700,
        color: Colors.black,
        fontFamily: FontManager.fontFamilyTiemposText,
      ),
    );
  }

  Widget _buildLocalObjectiveItem(
      int index, String objective, GoDetailsProvider goDetailsProvider) {
    // Get the LO object to check if it's from library
    final lo = goDetailsProvider.currentGoModel?.localObjectivesList?[index];
    final isLibraryLo = lo?.workflowSource == 'library';
    final sourceInfo = lo?.workSource;

    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // LO number and text with source indicator
        InkWell(
          onTap: () {
            // Check if GO is validated before allowing LO selection
            final isGoValidated = goDetailsProvider
                    .currentGoModel?.globalObjectives?.isValidated ??
                false;

            if (!isGoValidated) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                      'Please validate the GO first before selecting Local Objectives.'),
                  backgroundColor: Colors.orange,
                  duration: Duration(seconds: 3),
                ),
              );
              return;
            }

            final loHasPathWay = goDetailsProvider
                    .currentGoModel?.localObjectivesList?[index].pathwayData !=
                null;

            if (!loHasPathWay) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                      'Please create a pathway for this LO before proceeding.'),
                  backgroundColor: Colors.orange,
                  duration: Duration(seconds: 3),
                ),
              );
              return;
            }

            // Show local objective details within the same screen
            goDetailsProvider.setShowLocalObjectiveDetails(index);
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    'LO-${index + 1}. $objective',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText,
                    ),
                  ),
                  // if (isLibraryLo) ...[
                  //   const SizedBox(width: 8),
                  //   Container(
                  //     padding: const EdgeInsets.symmetric(
                  //         horizontal: 6, vertical: 2),
                  //     decoration: BoxDecoration(
                  //       color: Colors.blue[100],
                  //       borderRadius: BorderRadius.circular(10),
                  //       border: Border.all(color: Colors.blue[300]!, width: 1),
                  //     ),
                  //     child: Text(
                  //       'Library',
                  //       style: TextStyle(
                  //         fontSize: 9,
                  //         fontWeight: FontWeight.w500,
                  //         color: Colors.blue[800],
                  //         fontFamily: FontManager.fontFamilyTiemposText,
                  //       ),
                  //     ),
                  //   ),
                  // ],
                ],
              ),
              if (isLibraryLo && sourceInfo != null) ...[
                const SizedBox(height: 2),
                Text(
                  sourceInfo,
                  style: TextStyle(
                    fontSize: 9,
                    fontWeight: FontWeight.w400,
                    color: Colors.grey[600],
                    fontFamily: FontManager.fontFamilyTiemposText,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ],
          ),
        ),
        const SizedBox(width: AppSpacing.sm),
        // Create pathway button
        InkWell(
          onTap: () {
            // Check if GO is validated before allowing pathway creation
            // final isGoValidated = goDetailsProvider
            //         .currentGoModel?.globalObjectives?.isValidated ??
            //     false;
            //
            // if (!isGoValidated) {
            //   ScaffoldMessenger.of(context).showSnackBar(
            //     const SnackBar(
            //       content: Text(
            //           'Please validate the GO first before creating pathways.'),
            //       backgroundColor: Colors.orange,
            //       duration: Duration(seconds: 3),
            //     ),
            //   );
            //   return;
            // }

            goDetailsProvider.togglePathwayCreation(index);
          },
          child: const Text(
            'Create pathway',
            style: TextStyle(
              decoration: TextDecoration.underline,
              decorationColor: Color(0xFF007AFF),
              fontSize: 10,
              fontWeight: FontWeight.w500,
              height: 2.0,
              color: Colors.blue,
            ),
          ),
        ),

        Spacer(),
        InkWell(
          onTap: () {
            // Check if GO is validated before allowing LO insertion
            // final isGoValidated = goDetailsProvider
            //         .currentGoModel?.globalObjectives?.isValidated ??
            //     false;

            // if (!isGoValidated) {
            //   ScaffoldMessenger.of(context).showSnackBar(
            //     const SnackBar(
            //       content: Text(
            //           'Please validate the GO first before adding LO insertions.'),
            //       backgroundColor: Colors.orange,
            //       duration: Duration(seconds: 3),
            //     ),
            //   );
            //   return;
            // }

            goDetailsProvider.toggleLoInsertion(index);
          },
          child: const Icon(
            Icons.add,
            color: Colors.black,
            size: 18,
          ),
        ),
      ],
    );
  }

  Widget _buildLocalObjectiveInput(
      BuildContext context, GoDetailsProvider goDetailsProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Container(
                height: 35,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(2),
                ),
                child: TextField(
                  controller: goDetailsProvider.localObjectiveController,
                  onChanged: (value) {
                    // Clear validation error when user starts typing
                    if (goDetailsProvider.loValidationError != null) {
                      goDetailsProvider.clearValidationErrors();
                    }
                  },
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    isDense: true,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    fillColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 4, vertical: 12),
                    hintText: 'Type LO name with full stop (.)',
                    hintStyle: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                        fontFamily: FontManager.fontFamilyTiemposText),
                  ),
                  style: const TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText),
                ),
              ),
            ),
            const SizedBox(width: 8),
            // Tick icon for local objective
            InkWell(
              onTap: () {
                // First validate the LO field
                if (!goDetailsProvider.validateLocalObjectiveField()) {
                  return; // Stop if validation fails
                }
                goDetailsProvider.processLocalObjectives();
                // Toggle footer visibility
                goDetailsProvider.toggleFooterVisibility();
              },
              child: Container(
                width: 24,
                height: 24,
                decoration: const BoxDecoration(
                  color: Color(0xFF007AFF),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 14,
                ),
              ),
            ),
          ],
        ),
        // Validation error message for LO field
        if (goDetailsProvider.loValidationError != null)
          Padding(
            padding: const EdgeInsets.only(top: 2),
            child: Text(
              goDetailsProvider.loValidationError!,
              style: const TextStyle(
                fontSize: 8,
                color: Colors.red,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildLoInsertionField(
      int loIndex, GoDetailsProvider goDetailsProvider) {
    final controller = goDetailsProvider.getLoInsertionController(loIndex);
    // if (controller == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 10),
        Row(
          children: [
            Expanded(
              child: Container(
                height: 35,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(2),
                ),
                child: TextField(
                  controller: controller,
                  decoration: const InputDecoration(
                    border: InputBorder.none,
                    isDense: true,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    fillColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    contentPadding:
                        EdgeInsets.symmetric(horizontal: 4, vertical: 12),
                    hintText: 'Type LO names with full stop (.)',
                    hintStyle: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey,
                        fontFamily: FontManager.fontFamilyTiemposText),
                  ),
                  style: const TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                      fontFamily: FontManager.fontFamilyTiemposText),
                ),
              ),
            ),
            const SizedBox(width: 8),
            // Add icon for inserting LOs
            Row(
              children: [
                // Close icon
                InkWell(
                  onTap: () {
                    // Close the text field and revert to the previous state
                    goDetailsProvider.toggleLoInsertion(
                        loIndex); // Toggle LO insertion state
                  },
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.close,
                      color: Colors.black,
                      size: 14,
                    ),
                  ),
                ),
                const SizedBox(width: 8), // Space between icons

                // Check icon
                InkWell(
                  onTap: () {
                    if (!goDetailsProvider
                        .validateLocalObjectiveInsertionField()) {
                      return; // Stop if validation fails
                    }
                    goDetailsProvider.processLoInsertion(loIndex);
                    goDetailsProvider.toggleLoInsertion(loIndex);
                  },
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: const BoxDecoration(
                      color: Color(0xFF007AFF),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 14,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        if (goDetailsProvider.loInsertionValidationError != null)
          Padding(
            padding: const EdgeInsets.only(top: 2),
            child: Text(
              goDetailsProvider.loInsertionValidationError!,
              style: const TextStyle(
                fontSize: 8,
                color: Colors.red,
                fontFamily: FontManager.fontFamilyTiemposText,
              ),
            ),
          ),
      ],
    );
  }

  // Helper method to get responsive dropdown width
  double _getDropdownWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth <= 1366) {
      return 100.0;
    } else {
      return (screenWidth * 100) / 1366; // Scale proportionally
    }
  }

  double _getAttributeDropdownWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth <= 1366) {
      return 160.0;
    } else {
      return (screenWidth * 160) / 1366; // Scale proportionally
    }
  }

  Widget _buildPathwayRoleField(int loIndex,
      GoDetailsProvider goDetailsProvider, MyLibraryProvider libraryProvider) {
    // Fetch library data if not already loaded
    // if (!libraryProvider.isLoading && libraryProvider.roles.isEmpty) {
    //   WidgetsBinding.instance.addPostFrameCallback((_) {
    //     libraryProvider.fetchLibraryData();
    //   });
    // }

    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Select Role',
        list: libraryProvider.isLoading
            ? ['Loading roles...']
            : libraryProvider.roles.isEmpty
                ? ['No roles available']
                : libraryProvider.getRoleNames(),
        value: goDetailsProvider
            .getPathwaySelectedMultipleRoles(loIndex)
            .map((role) => role.name ?? '')
            .toList(),
        isMultiSelect: true,
        onChanged: (selectedValues) {
          if (!libraryProvider.isLoading && selectedValues is List<String>) {
            // Filter out loading/error messages
            final validRoleNames = selectedValues
                .where((value) =>
                    value != 'Loading roles...' &&
                    value != 'No roles available')
                .toList();

            // Convert role names to PostgresRole objects
            final selectedRoles = <PostgresRole>[];
            for (final roleName in validRoleNames) {
              final selectedLibraryRole =
                  libraryProvider.getRoleByName(roleName);
              if (selectedLibraryRole != null) {
                final postgresRole = PostgresRole(
                  roleId: selectedLibraryRole.roleId,
                  name: selectedLibraryRole.name,
                  description: selectedLibraryRole.description,
                  tenantId: null,
                  createdAt: selectedLibraryRole.createdAt,
                  updatedAt: selectedLibraryRole.updatedAt,
                );
                selectedRoles.add(postgresRole);
              }
            }

            goDetailsProvider.setPathwaySelectedMultipleRoles(
                loIndex, selectedRoles);
          }
        },
      ),
    );
  }

  Widget _buildPathwayTypeField(
      int loIndex, GoDetailsProvider goDetailsProvider) {
    final typeOptions = [
      'Sequential',
      'Alternative',
      'Parallel',
      'Recursive',
      'Terminal'
    ];

    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Select Type',
        list: typeOptions,
        value: goDetailsProvider.getPathwaySelectedType(loIndex),
        onChanged: (selectedType) {
          goDetailsProvider.setPathwaySelectedType(loIndex, selectedType);
        },
      ),
    );
  }

  Widget _buildPathwayLOField(
      int loIndex, GoDetailsProvider goDetailsProvider) {
    final availableLOs =
        goDetailsProvider.getAvailableLOsForSequential(loIndex);

    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Select LO',
        list: availableLOs.isEmpty ? ['No LOs available'] : availableLOs,
        value: goDetailsProvider.getPathwaySelectedLO(loIndex),
        onChanged: (selectedLO) {
          if (availableLOs.isNotEmpty && selectedLO != 'No LOs available') {
            goDetailsProvider.setPathwaySelectedLO(loIndex, selectedLO);
          }
        },
      ),
    );
  }

  // Additional pathway creation helper methods
  Widget _buildRecursiveInputField(
      int loIndex, GoDetailsProvider goDetailsProvider) {
    return Container(
      height: 35,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(2),
      ),
      child: TextField(
        decoration: const InputDecoration(
          border: InputBorder.none,
          isDense: true,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          fillColor: Colors.transparent,
          hoverColor: Colors.transparent,
          contentPadding: EdgeInsets.symmetric(horizontal: 4, vertical: 12),
          hintText: 'Input number',
          hintStyle: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.w500,
            color: Colors.grey,
            fontFamily: FontManager.fontFamilyTiemposText,
          ),
        ),
        style: const TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: Colors.black,
          fontFamily: FontManager.fontFamilyTiemposText,
        ),
      ),
    );
  }

  // Dynamic dropdown methods for pathway entries
  Widget _buildDynamicLODropdown(
      int loIndex, int entryIndex, GoDetailsProvider goDetailsProvider) {
    final availableLOs =
        goDetailsProvider.getAvailableLOsForSequential(loIndex);
    final pathwayEntry = goDetailsProvider.getPathwayEntry(loIndex, entryIndex);

    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Select LO',
        list: availableLOs.isEmpty ? ['No LOs available'] : availableLOs,
        value: pathwayEntry?.selectedLO,
        onChanged: (selectedLO) {
          if (availableLOs.isNotEmpty && selectedLO != 'No LOs available') {
            goDetailsProvider.setPathwayEntrySelectedLO(
                loIndex, entryIndex, selectedLO);
          }
        },
      ),
    );
  }

  Widget _buildDynamicEntityAttributeDropdown({
    required int loIndex,
    required int entryIndex,
    required bool isAfterCondition,
    required GoDetailsProvider goDetailsProvider,
  }) {
    // Get entity attributes from ObjectCreationProvider
    final objectCreationProvider =
        Provider.of<ObjectCreationProvider>(context, listen: false);

    List<String> entityAttributes = [];

    // Check if there's a current object with attributes
    if (objectCreationProvider.currentObject?.attributes != null) {
      entityAttributes = objectCreationProvider.currentObject!.attributes!
          .where((attr) => attr.name != null && attr.name!.isNotEmpty)
          .map((attr) => attr.name!)
          .toList();
    }

    // If no attributes from current object, try to get from objects list
    if (entityAttributes.isEmpty && objectCreationProvider.objects.isNotEmpty) {
      // Get attributes from the first available object that has attributes
      for (final obj in objectCreationProvider.objects) {
        if (obj.attributes != null && obj.attributes!.isNotEmpty) {
          entityAttributes = obj.attributes!
              .where((attr) => attr.name != null && attr.name!.isNotEmpty)
              .map((attr) => attr.name!)
              .toList();
          break;
        }
      }
    }

    final pathwayEntry = goDetailsProvider.getPathwayEntry(loIndex, entryIndex);

    String? currentValue = isAfterCondition
        ? pathwayEntry?.entityAttributeAfterCondition
        : pathwayEntry?.entityAttribute;

    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Entity Attribute',
        list: entityAttributes,
        value: currentValue,
        onChanged: (value) {
          if (value != null) {
            if (isAfterCondition) {
              goDetailsProvider.setPathwayEntryEntityAttributeAfterCondition(
                  loIndex, entryIndex, value);
            } else {
              goDetailsProvider.setPathwayEntryEntityAttribute(
                  loIndex, entryIndex, value);
            }
          }
        },
      ),
    );
  }

  Widget _buildDynamicConditionDropdown({
    required int loIndex,
    required int entryIndex,
    required GoDetailsProvider goDetailsProvider,
  }) {
    const List<String> conditions = [
      '==',
      '!=',
      '<',
      '<=',
      '>',
      '>=',
    ];

    final pathwayEntry = goDetailsProvider.getPathwayEntry(loIndex, entryIndex);

    return SizedBox(
      height: 24,
      child: CustomDropdownWidget(
        label: 'Condition',
        list: conditions,
        value: pathwayEntry?.condition,
        onChanged: (value) {
          if (value != null) {
            goDetailsProvider.setPathwayEntryCondition(
                loIndex, entryIndex, value);
          }
        },
      ),
    );
  }
}
